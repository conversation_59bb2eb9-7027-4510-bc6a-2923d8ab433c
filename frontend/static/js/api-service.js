class ApiService {
    constructor(config) {
        this.config = config;
    }

    async getUsers() {
        try {
            const response = await fetch(`${this.config.BASE_URL}${this.config.USERS}`);
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching users:', error);
            throw error;
        }
    }

    async getTransactions() {
        try {
            const response = await fetch(`${this.config.BASE_URL}${this.config.TRANSACTIONS}`);
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching transactions:', error);
            throw error;
        }
    }

    async getUserRelationships(userId) {
        try {
            const response = await fetch(`${this.config.BASE_URL}${this.config.USER_RELATIONSHIPS}${userId}`);
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error(`Error fetching relationships for user ${userId}:`, error);
            throw error;
        }
    }

    async getBusinessRelationships(userId) {
        try {
            const response = await fetch(`${this.config.BASE_URL}${this.config.BUSINESS_RELATIONSHIPS}${userId}`);
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error(`Error fetching business relationships for user ${userId}:`, error);
            throw error;
        }
    }

    async getTransactionRelationships(transactionId) {
        try {
            const response = await fetch(`${this.config.BASE_URL}${this.config.TRANSACTION_RELATIONSHIPS}${transactionId}`);
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error(`Error fetching relationships for transaction ${transactionId}:`, error);
            throw error;
        }
    }

    async detectRelationships() {
        try {
            const response = await fetch(`${this.config.BASE_URL}${this.config.DETECT_RELATIONSHIPS}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error detecting relationships:', error);
            throw error;
        }
    }
}

const apiService = new ApiService(CONFIG.API);
