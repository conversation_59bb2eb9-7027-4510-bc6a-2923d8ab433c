document.addEventListener('DOMContentLoaded', function() {
    const graphContainer = document.getElementById('graph-container');
    const loadingOverlay = document.getElementById('loading-overlay');
    const nodeDetails = document.getElementById('node-details');
    const nodeDetailsTitle = document.getElementById('node-details-title');
    const nodeDetailsContent = document.getElementById('node-details-content');
    const closeDetails = document.getElementById('close-details');
    const sidebar = document.querySelector('.sidebar');
    const searchInput = document.getElementById('search-input');
    const searchBtn = document.getElementById('search-btn');
    const layoutSelect = document.getElementById('layout-select');
    const applyLayout = document.getElementById('apply-layout');
    const refreshData = document.getElementById('refresh-data');
    const detectRelationships = document.getElementById('detect-relationships');
    const exportImage = document.getElementById('export-image');
    const resetView = document.getElementById('reset-view');
    const zoomIn = document.getElementById('zoom-in');
    const zoomOut = document.getElementById('zoom-out');
    const fitGraph = document.getElementById('fit-graph');
    const nodeCount = document.getElementById('node-count');
    const edgeCount = document.getElementById('edge-count');

    const filterUsers = document.getElementById('filter-users');
    const filterCompanies = document.getElementById('filter-companies');
    const filterTransactions = document.getElementById('filter-transactions');
    const filterParentChild = document.getElementById('filter-parent-child');
    const filterDirector = document.getElementById('filter-director');
    const filterShareholder = document.getElementById('filter-shareholder');
    const filterLegalEntity = document.getElementById('filter-legal-entity');
    const filterComposite = document.getElementById('filter-composite');
    const filterSharedAttributes = document.getElementById('filter-shared-attributes');
    const filterTransaction = document.getElementById('filter-transaction');

    let cy = null;

    let graphData = {
        nodes: [],
        edges: []
    };

    function initGraph() {
        cy = cytoscape({
            container: graphContainer,
            elements: [],
            style: GRAPH_STYLES,
            layout: CONFIG.LAYOUTS['cola'],
            minZoom: 0.1,
            maxZoom: 3,
            wheelSensitivity: 0.2
        });

        cy.on('tap', 'node', function(evt) {
            const node = evt.target;
            showNodeDetails(node);
        });

        cy.on('tap', function(evt) {
            if (evt.target === cy) {
                hideNodeDetails();
            }
        });
    }

    async function loadData() {
        showLoading();

        try {

            graphData = {
                nodes: [],
                edges: []
            };

            const [users, transactions] = await Promise.all([
                apiService.getUsers(),
                apiService.getTransactions()
            ]);

            for (const user of users) {
                const nodeType = GraphUtils.getNodeType(user);

                graphData.nodes.push({
                    data: {
                        id: user.id,
                        label: GraphUtils.getNodeLabel(user),
                        type: nodeType,
                        size: GraphUtils.getNodeSize(user),
                        ...user
                    }
                });

                try {
                    const userRelationships = await apiService.getUserRelationships(user.id);
                    processUserRelationships(userRelationships);

                    if (nodeType !== CONFIG.NODE_TYPES.TRANSACTION) {
                        try {
                            const businessRelationships = await apiService.getBusinessRelationships(user.id);
                            processBusinessRelationships(businessRelationships);
                        } catch (error) {
                            console.warn(`Could not fetch business relationships for ${user.id}:`, error);
                        }
                    }
                } catch (error) {
                    console.warn(`Could not fetch relationships for ${user.id}:`, error);
                }
            }

            for (const transaction of transactions) {
                graphData.nodes.push({
                    data: {
                        id: transaction.id,
                        label: GraphUtils.getNodeLabel(transaction),
                        type: CONFIG.NODE_TYPES.TRANSACTION,
                        size: GraphUtils.getNodeSize(transaction),
                        ...transaction
                    }
                });

                try {
                    const transactionRelationships = await apiService.getTransactionRelationships(transaction.id);
                    processTransactionRelationships(transactionRelationships);
                } catch (error) {
                    console.warn(`Could not fetch relationships for transaction ${transaction.id}:`, error);
                }
            }

            const uniqueEdges = {};
            graphData.edges.forEach(edge => {
                const edgeId = edge.data.id;
                uniqueEdges[edgeId] = edge;
            });
            graphData.edges = Object.values(uniqueEdges);

            updateGraph();
        } catch (error) {
            console.error('Error loading data:', error);
            alert('Error loading data. Please check the console for details.');
        } finally {
            hideLoading();
        }
    }

    function processUserRelationships(relationshipData) {
        if (!relationshipData || !relationshipData.relationships) return;

        const { user, relationships } = relationshipData;
        const userId = user.id;

        if (relationships.outgoing) {
            relationships.outgoing.forEach(rel => {
                if (!rel.node || !rel.type) return;

                const targetId = rel.node.id;
                if (!targetId) return;

                if (!graphData.nodes.some(n => n.data.id === targetId)) {
                    const nodeType = GraphUtils.getNodeType(rel.node);
                    graphData.nodes.push({
                        data: {
                            id: targetId,
                            label: GraphUtils.getNodeLabel(rel.node),
                            type: nodeType,
                            size: GraphUtils.getNodeSize(rel.node),
                            ...rel.node
                        }
                    });
                }

                const edgeId = GraphUtils.getEdgeId(userId, targetId, rel.type);
                graphData.edges.push({
                    data: {
                        id: edgeId,
                        source: userId,
                        target: targetId,
                        type: rel.type,
                        label: rel.type
                    }
                });
            });
        }

        if (relationships.incoming) {
            relationships.incoming.forEach(rel => {
                if (!rel.node || !rel.type) return;

                const sourceId = rel.node.id;
                if (!sourceId) return;

                if (!graphData.nodes.some(n => n.data.id === sourceId)) {
                    const nodeType = GraphUtils.getNodeType(rel.node);
                    graphData.nodes.push({
                        data: {
                            id: sourceId,
                            label: GraphUtils.getNodeLabel(rel.node),
                            type: nodeType,
                            size: GraphUtils.getNodeSize(rel.node),
                            ...rel.node
                        }
                    });
                }

                const edgeId = GraphUtils.getEdgeId(sourceId, userId, rel.type);
                graphData.edges.push({
                    data: {
                        id: edgeId,
                        source: sourceId,
                        target: userId,
                        type: rel.type,
                        label: rel.type
                    }
                });
            });
        }
    }

    function processBusinessRelationships(relationshipData) {
        if (!relationshipData || !relationshipData.business_relationships) return;

        const { user, business_relationships } = relationshipData;
        const userId = user.id;

        if (business_relationships.outgoing) {
            business_relationships.outgoing.forEach(rel => {
                if (!rel.node || !rel.type) return;

                const targetId = rel.node.id;
                if (!targetId) return;

                if (!graphData.nodes.some(n => n.data.id === targetId)) {
                    const nodeType = GraphUtils.getNodeType(rel.node);
                    graphData.nodes.push({
                        data: {
                            id: targetId,
                            label: GraphUtils.getNodeLabel(rel.node),
                            type: nodeType,
                            size: GraphUtils.getNodeSize(rel.node),
                            ...rel.node
                        }
                    });
                }

                const edgeId = GraphUtils.getEdgeId(userId, targetId, rel.type);
                graphData.edges.push({
                    data: {
                        id: edgeId,
                        source: userId,
                        target: targetId,
                        type: rel.type,
                        label: rel.type,
                        properties: rel.properties
                    }
                });
            });
        }

        if (business_relationships.incoming) {
            business_relationships.incoming.forEach(rel => {
                if (!rel.node || !rel.type) return;

                const sourceId = rel.node.id;
                if (!sourceId) return;

                if (!graphData.nodes.some(n => n.data.id === sourceId)) {
                    const nodeType = GraphUtils.getNodeType(rel.node);
                    graphData.nodes.push({
                        data: {
                            id: sourceId,
                            label: GraphUtils.getNodeLabel(rel.node),
                            type: nodeType,
                            size: GraphUtils.getNodeSize(rel.node),
                            ...rel.node
                        }
                    });
                }

                const edgeId = GraphUtils.getEdgeId(sourceId, userId, rel.type);
                graphData.edges.push({
                    data: {
                        id: edgeId,
                        source: sourceId,
                        target: userId,
                        type: rel.type,
                        label: rel.type,
                        properties: rel.properties
                    }
                });
            });
        }
    }

    function processTransactionRelationships(relationshipData) {
        if (!relationshipData) return;

        const { transaction, relationships } = relationshipData;
        const transactionId = transaction.id;

        if (relationships.incoming_users) {
            relationships.incoming_users.forEach(rel => {
                if (!rel.node || !rel.type) return;

                const sourceId = rel.node.id;
                if (!sourceId) return;

                if (!graphData.nodes.some(n => n.data.id === sourceId)) {
                    const nodeType = GraphUtils.getNodeType(rel.node);
                    graphData.nodes.push({
                        data: {
                            id: sourceId,
                            label: GraphUtils.getNodeLabel(rel.node),
                            type: nodeType,
                            size: GraphUtils.getNodeSize(rel.node),
                            ...rel.node
                        }
                    });
                }

                const edgeId = GraphUtils.getEdgeId(sourceId, transactionId, rel.type);
                graphData.edges.push({
                    data: {
                        id: edgeId,
                        source: sourceId,
                        target: transactionId,
                        type: rel.type,
                        label: rel.type
                    }
                });
            });
        }

        if (relationships.outgoing_users) {
            relationships.outgoing_users.forEach(rel => {
                if (!rel.node || !rel.type) return;

                const targetId = rel.node.id;
                if (!targetId) return;

                if (!graphData.nodes.some(n => n.data.id === targetId)) {
                    const nodeType = GraphUtils.getNodeType(rel.node);
                    graphData.nodes.push({
                        data: {
                            id: targetId,
                            label: GraphUtils.getNodeLabel(rel.node),
                            type: nodeType,
                            size: GraphUtils.getNodeSize(rel.node),
                            ...rel.node
                        }
                    });
                }

                const edgeId = GraphUtils.getEdgeId(transactionId, targetId, rel.type);
                graphData.edges.push({
                    data: {
                        id: edgeId,
                        source: transactionId,
                        target: targetId,
                        type: rel.type,
                        label: rel.type
                    }
                });
            });
        }

        if (relationships.linked_transactions) {
            relationships.linked_transactions.forEach(rel => {
                if (!rel.node || !rel.type) return;

                const linkedId = rel.node.id;
                if (!linkedId) return;

                if (!graphData.nodes.some(n => n.data.id === linkedId)) {
                    graphData.nodes.push({
                        data: {
                            id: linkedId,
                            label: GraphUtils.getNodeLabel(rel.node),
                            type: CONFIG.NODE_TYPES.TRANSACTION,
                            size: GraphUtils.getNodeSize(rel.node),
                            ...rel.node
                        }
                    });
                }

                const edgeId = GraphUtils.getEdgeId(transactionId, linkedId, rel.type);
                graphData.edges.push({
                    data: {
                        id: edgeId,
                        source: transactionId,
                        target: linkedId,
                        type: rel.type,
                        label: rel.type
                    }
                });
            });
        }
    }

    function updateGraph() {

        const filteredNodes = graphData.nodes.filter(node => {
            const nodeType = node.data.type;

            if (nodeType === CONFIG.NODE_TYPES.USER && !filterUsers.checked) {
                return false;
            }

            if (nodeType === CONFIG.NODE_TYPES.COMPANY && !filterCompanies.checked) {
                return false;
            }

            if (nodeType === CONFIG.NODE_TYPES.TRANSACTION && !filterTransactions.checked) {
                return false;
            }

            return true;
        });

        const filteredNodeIds = filteredNodes.map(node => node.data.id);

        const filteredEdges = graphData.edges.filter(edge => {
            const edgeType = edge.data.type;
            const sourceId = edge.data.source;
            const targetId = edge.data.target;

            if (!filteredNodeIds.includes(sourceId) || !filteredNodeIds.includes(targetId)) {
                return false;
            }

            if (GraphUtils.relationshipBelongsToCategory(edgeType, 'PARENT_CHILD') && !filterParentChild.checked) {
                return false;
            }

            if (GraphUtils.relationshipBelongsToCategory(edgeType, 'DIRECTOR') && !filterDirector.checked) {
                return false;
            }

            if (GraphUtils.relationshipBelongsToCategory(edgeType, 'SHAREHOLDER') && !filterShareholder.checked) {
                return false;
            }

            if (GraphUtils.relationshipBelongsToCategory(edgeType, 'LEGAL_ENTITY') && !filterLegalEntity.checked) {
                return false;
            }

            if (GraphUtils.relationshipBelongsToCategory(edgeType, 'COMPOSITE') && !filterComposite.checked) {
                return false;
            }

            if (GraphUtils.relationshipBelongsToCategory(edgeType, 'SHARED_ATTRIBUTES') && !filterSharedAttributes.checked) {
                return false;
            }

            if (GraphUtils.relationshipBelongsToCategory(edgeType, 'TRANSACTION') && !filterTransaction.checked) {
                return false;
            }

            return true;
        });

        cy.elements().remove();
        cy.add([...filteredNodes, ...filteredEdges]);

        applyCurrentLayout();

        updateCounts();
    }

    function applyCurrentLayout() {
        const layoutName = layoutSelect.value;
        const layout = cy.layout(CONFIG.LAYOUTS[layoutName]);
        layout.run();
    }

    function updateCounts() {
        nodeCount.textContent = `Nodes: ${cy.nodes().length}`;
        edgeCount.textContent = `Edges: ${cy.edges().length}`;
    }

    function showNodeDetails(node) {

        const data = node.data();
        const type = data.type;
        let title = '';

        if (type === CONFIG.NODE_TYPES.TRANSACTION) {
            if (data.metadata && data.metadata.purpose) {
                title = data.metadata.purpose.charAt(0).toUpperCase() + data.metadata.purpose.slice(1);
            } else {
                title = `Transaction ${data.id.replace('tx', '#')}`;
            }
        } else if (type === CONFIG.NODE_TYPES.COMPANY) {
            title = data.company_name || data.name;
        } else {
            title = data.name;
        }

        nodeDetailsTitle.textContent = title;
        nodeDetailsContent.innerHTML = GraphUtils.generateNodeDetailsHTML(node);
        nodeDetails.classList.add('visible');

        document.querySelectorAll('.node-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const nodeId = this.getAttribute('data-node-id');
                const targetNode = cy.getElementById(nodeId);
                if (targetNode.length > 0) {
                    cy.fit(targetNode, 100);
                    cy.center(targetNode);
                    showNodeDetails(targetNode);
                }
            });
        });
    }

    function hideNodeDetails() {
        nodeDetails.classList.remove('visible');
    }

    function showLoading() {
        loadingOverlay.style.display = 'flex';
    }

    function hideLoading() {
        loadingOverlay.style.display = 'none';
    }

    function searchNodes(query) {
        if (!query) return;

        const lowerQuery = query.toLowerCase();
        const matchingNodes = cy.nodes().filter(node => {
            const label = node.data('label').toLowerCase();
            return label.includes(lowerQuery);
        });

        if (matchingNodes.length > 0) {
            cy.fit(matchingNodes, 100);

            cy.nodes().removeClass('highlighted');
            matchingNodes.addClass('highlighted');

            if (matchingNodes.length === 1) {
                showNodeDetails(matchingNodes[0]);
            }
        } else {
            alert('No matching nodes found.');
        }
    }

    function exportGraphImage() {
        const png64 = cy.png({
            output: 'blob',
            bg: 'white',
            full: true,
            scale: 2
        });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(png64);
        link.download = 'graph-export.png';
        link.click();
    }

    function init() {

        initGraph();

        loadData();

        closeDetails.addEventListener('click', hideNodeDetails);

        searchBtn.addEventListener('click', function() {
            searchNodes(searchInput.value);
        });

        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchNodes(searchInput.value);
            }
        });

        applyLayout.addEventListener('click', applyCurrentLayout);

        refreshData.addEventListener('click', loadData);

        detectRelationships.addEventListener('click', async function() {
            showLoading();
            try {
                await apiService.detectRelationships();
                await loadData();
                alert('Relationships detected and created successfully.');
            } catch (error) {
                console.error('Error detecting relationships:', error);
                alert('Error detecting relationships. Please check the console for details.');
            } finally {
                hideLoading();
            }
        });

        exportImage.addEventListener('click', exportGraphImage);

        resetView.addEventListener('click', function() {
            cy.fit();
            cy.center();
            cy.nodes().removeClass('highlighted');
            hideNodeDetails();
        });

        zoomIn.addEventListener('click', function() {
            cy.zoom(cy.zoom() * 1.2);
        });

        zoomOut.addEventListener('click', function() {
            cy.zoom(cy.zoom() / 1.2);
        });

        fitGraph.addEventListener('click', function() {
            cy.fit();
        });

        const filterElements = [
            filterUsers, filterCompanies, filterTransactions,
            filterParentChild, filterDirector, filterShareholder,
            filterLegalEntity, filterComposite, filterSharedAttributes,
            filterTransaction
        ];

        filterElements.forEach(filter => {
            filter.addEventListener('change', updateGraph);
        });
    }

    init();
});
