<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relationship Graph Visualization</title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="/static/img/favicon.png">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/static/css/styles.css">
    <!-- Cytoscape.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cytoscape/3.25.0/cytoscape.min.js"></script>
    <!-- Cola.js for layout -->
    <script src="https://unpkg.com/webcola/WebCola/cola.min.js"></script>
    <!-- Cytoscape extensions -->
    <script src="https://unpkg.com/cytoscape-cola/cytoscape-cola.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/cytoscape-context-menus@4.1.0/cytoscape-context-menus.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/cytoscape-context-menus@4.1.0/cytoscape-context-menus.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h3>Graph Explorer</h3>
            </div>
            <div class="sidebar-content">
                <!-- Search Section -->
                <div class="sidebar-section">
                    <h5>Search</h5>
                    <div class="input-group mb-3">
                        <input type="text" id="search-input" class="form-control" placeholder="Search nodes...">
                        <button class="btn btn-primary" id="search-btn"><i class="fas fa-search"></i></button>
                    </div>
                </div>

                <!-- Filters Section -->
                <div class="sidebar-section">
                    <h5>Filters</h5>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="filter-users" checked>
                        <label class="form-check-label" for="filter-users">
                            Users
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="filter-companies" checked>
                        <label class="form-check-label" for="filter-companies">
                            Companies
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="filter-transactions" checked>
                        <label class="form-check-label" for="filter-transactions">
                            Transactions
                        </label>
                    </div>
                    <hr>
                    <h6>Relationship Types</h6>
                    <div class="relationship-filters">
                        <!-- Business Relationships -->
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="filter-parent-child" checked>
                            <label class="form-check-label" for="filter-parent-child">
                                Parent-Child
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="filter-director" checked>
                            <label class="form-check-label" for="filter-director">
                                Director
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="filter-shareholder" checked>
                            <label class="form-check-label" for="filter-shareholder">
                                Shareholder
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="filter-legal-entity" checked>
                            <label class="form-check-label" for="filter-legal-entity">
                                Legal Entity
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="filter-composite" checked>
                            <label class="form-check-label" for="filter-composite">
                                Composite
                            </label>
                        </div>

                        <!-- User-to-User Relationships -->
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="filter-shared-attributes" checked>
                            <label class="form-check-label" for="filter-shared-attributes">
                                Shared Attributes
                            </label>
                        </div>

                        <!-- Transaction Relationships -->
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="filter-transaction" checked>
                            <label class="form-check-label" for="filter-transaction">
                                Transaction Links
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Layout Section -->
                <div class="sidebar-section">
                    <h5>Layout</h5>
                    <select id="layout-select" class="form-select">
                        <option value="cola">Cola</option>
                        <option value="circle">Circle</option>
                        <option value="concentric">Concentric</option>
                        <option value="grid">Grid</option>
                        <option value="breadthfirst">Breadth-first</option>
                    </select>
                    <button id="apply-layout" class="btn btn-primary btn-sm mt-2">Apply Layout</button>
                </div>

                <!-- Actions Section -->
                <div class="sidebar-section">
                    <h5>Actions</h5>
                    <button id="refresh-data" class="btn btn-outline-primary btn-sm mb-2 w-100">
                        <i class="fas fa-sync-alt"></i> Refresh Data
                    </button>
                    <button id="detect-relationships" class="btn btn-outline-primary btn-sm mb-2 w-100">
                        <i class="fas fa-project-diagram"></i> Detect Relationships
                    </button>
                    <button id="export-image" class="btn btn-outline-primary btn-sm mb-2 w-100">
                        <i class="fas fa-download"></i> Export as Image
                    </button>
                    <button id="reset-view" class="btn btn-outline-secondary btn-sm w-100">
                        <i class="fas fa-undo"></i> Reset View
                    </button>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Toolbar -->
            <div class="toolbar">
                <div class="toolbar-left">
                    <div class="btn-group" role="group">
                        <button id="zoom-in" class="btn btn-sm btn-light"><i class="fas fa-search-plus"></i></button>
                        <button id="zoom-out" class="btn btn-sm btn-light"><i class="fas fa-search-minus"></i></button>
                        <button id="fit-graph" class="btn btn-sm btn-light"><i class="fas fa-expand"></i></button>
                    </div>
                </div>
                <div class="toolbar-center">
                    <h4>User & Transaction Relationship Graph</h4>
                </div>
                <div class="toolbar-right">
                    <span id="node-count" class="badge bg-secondary">Nodes: 0</span>
                    <span id="edge-count" class="badge bg-secondary">Edges: 0</span>
                </div>
            </div>

            <!-- Graph Container -->
            <div id="graph-container"></div>

            <!-- Node Details Panel -->
            <div id="node-details" class="node-details">
                <div class="node-details-header">
                    <h5 id="node-details-title">Node Details</h5>
                    <button id="close-details" class="btn btn-sm btn-light"><i class="fas fa-times"></i></button>
                </div>
                <div id="node-details-content" class="node-details-content">
                    <p>Select a node to view details.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay">
        <div class="spinner-border text-light" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p>Loading graph data...</p>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/config.js"></script>
    <script src="/static/js/graph-styles.js"></script>
    <script src="/static/js/graph-utils.js"></script>
    <script src="/static/js/api-service.js"></script>
    <script src="/static/js/main.js"></script>
</body>
</html>
